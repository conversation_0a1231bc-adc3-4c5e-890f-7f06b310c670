#!/usr/bin/env python3
"""
ML-Enhanced Strategy Engine for Trading Bot
Combines Machine Learning predictions with rule-based strategies
"""

import pandas as pd
import numpy as np
import joblib
from datetime import datetime
from strategy_engine import StrategyEngine
from config import STRATEGY_CONFIG, MODEL_CONFIG, TRADING_CONFIG
from utils import print_colored, format_price

class MLStrategyEngine(StrategyEngine):
    def __init__(self):
        """Initialize the ML-enhanced strategy engine"""
        super().__init__()  # Initialize parent class (loads models)
        self.ml_enabled = True
        
    def prepare_features_for_ml(self, df):
        """Prepare features for ML model prediction"""
        try:
            if len(df) < 50:
                return None
                
            # Get the current candle data
            current = df.iloc[-1]
            
            # Basic OHLCV features
            features = {
                'open': current['open'],
                'high': current['high'], 
                'low': current['low'],
                'close': current['close'],
                'volume': current['volume']
            }
            
            # Technical indicators
            if 'rsi' in current:
                features['rsi'] = current['rsi']
            if 'macd' in current:
                features['macd'] = current['macd']
            if 'macd_signal' in current:
                features['macd_signal'] = current['macd_signal']
            if 'sma_20' in current:
                features['sma_20'] = current['sma_20']
            if 'ema_12' in current:
                features['ema_12'] = current['ema_12']
            if 'ema_26' in current:
                features['ema_26'] = current['ema_26']
            if 'bb_upper' in current:
                features['bb_upper'] = current['bb_upper']
            if 'bb_lower' in current:
                features['bb_lower'] = current['bb_lower']
            if 'volume_sma' in current:
                features['volume_sma'] = current['volume_sma']
                
            # Engineered features
            prev = df.iloc[-2] if len(df) > 1 else current
            
            # Candle pattern features
            body_size = abs(current['close'] - current['open'])
            total_range = current['high'] - current['low']
            upper_wick = current['high'] - max(current['open'], current['close'])
            lower_wick = min(current['open'], current['close']) - current['low']
            
            features.update({
                'body_size': body_size,
                'total_range': total_range,
                'upper_wick': upper_wick,
                'lower_wick': lower_wick,
                'body_to_range': body_size / (total_range + 1e-8),
                'upper_wick_pct': upper_wick / (body_size + 1e-8),
                'lower_wick_pct': lower_wick / (body_size + 1e-8),
                'is_green': 1 if current['close'] > current['open'] else 0,
                'is_red': 1 if current['close'] < current['open'] else 0,
                'volume_change': current['volume'] / (prev['volume'] + 1e-8),
                'price_change': (current['close'] - prev['close']) / (prev['close'] + 1e-8),
                'high_change': (current['high'] - prev['high']) / (prev['high'] + 1e-8),
                'low_change': (current['low'] - prev['low']) / (prev['low'] + 1e-8)
            })
            
            # Support/Resistance features
            lookback = min(50, len(df))
            recent_highs = df['high'].tail(lookback)
            recent_lows = df['low'].tail(lookback)
            
            resistance = recent_highs.max()
            support = recent_lows.min()
            
            features.update({
                'distance_to_resistance': (resistance - current['close']) / (current['close'] + 1e-8),
                'distance_to_support': (current['close'] - support) / (current['close'] + 1e-8),
                'near_resistance': 1 if abs(current['high'] - resistance) / resistance < 0.001 else 0,
                'near_support': 1 if abs(current['low'] - support) / support < 0.001 else 0
            })
            
            # Trend features
            if len(df) >= 20:
                trend_period = min(20, len(df))
                recent_closes = df['close'].tail(trend_period)
                trend_slope = (recent_closes.iloc[-1] - recent_closes.iloc[0]) / len(recent_closes)
                
                features.update({
                    'trend_slope': trend_slope,
                    'is_uptrend': 1 if trend_slope > 0 else 0,
                    'is_downtrend': 1 if trend_slope < 0 else 0
                })
            
            return features
            
        except Exception as e:
            print_colored(f"❌ Error preparing ML features: {str(e)}", "ERROR")
            return None
    
    def predict_with_ml(self, df):
        """Get ML model prediction"""
        try:
            if not self.ml_enabled or self.model is None:
                return 'HOLD', 0.0
                
            # Prepare features
            features_dict = self.prepare_features_for_ml(df)
            if features_dict is None:
                return 'HOLD', 0.0
            
            # Create feature vector matching training features
            feature_vector = []
            for col in self.feature_columns:
                if col in features_dict:
                    feature_vector.append(features_dict[col])
                else:
                    feature_vector.append(0.0)  # Default value for missing features
            
            # Convert to pandas DataFrame with proper feature names to avoid sklearn warning
            import pandas as pd
            X_df = pd.DataFrame([feature_vector], columns=self.feature_columns)

            # Handle any remaining NaN or inf values
            X_df = X_df.fillna(0.0)
            X_df = X_df.replace([np.inf, -np.inf], 0.0)

            # Scale features (using DataFrame to maintain feature names)
            X_scaled = self.scaler.transform(X_df)
            
            # Get prediction
            prediction = self.model.predict(X_scaled)[0]
            probabilities = self.model.predict_proba(X_scaled)[0]
            confidence = probabilities.max()
            
            # Convert prediction to signal
            signal_name = self.label_encoder.inverse_transform([prediction])[0]
            
            return signal_name, confidence
            
        except Exception as e:
            print_colored(f"❌ Error in ML prediction: {str(e)}", "ERROR")
            return 'HOLD', 0.0
    
    def evaluate_ml_enhanced_strategies(self, df):
        """Evaluate strategies using ML + rule-based approach"""
        # Get rule-based signals
        rule_based_result = self.evaluate_all_strategies(df)
        
        # Get ML prediction
        ml_signal, ml_confidence = self.predict_with_ml(df)
        
        # Decision logic: Combine ML and rule-based signals
        final_signal, final_confidence, decision_method = self.combine_signals(
            rule_based_result, ml_signal, ml_confidence
        )
        
        return {
            'signal': final_signal,
            'confidence': final_confidence,
            'strategy': rule_based_result.get('strategy', 'ML'),
            'price': df.iloc[-1]['close'],
            'all_signals': rule_based_result['all_signals'],
            'ml_signal': ml_signal,
            'ml_confidence': ml_confidence,
            'rule_signal': rule_based_result['signal'],
            'rule_confidence': rule_based_result['confidence'],
            'decision_method': decision_method
        }
    
    def combine_signals(self, rule_result, ml_signal, ml_confidence):
        """Combine ML and rule-based signals intelligently"""
        rule_signal = rule_result['signal']
        rule_confidence = rule_result['confidence']
        
        # Decision logic
        if rule_signal != 'HOLD' and ml_signal != 'HOLD':
            if rule_signal == ml_signal:
                # Both agree - high confidence
                combined_confidence = min(0.95, (rule_confidence + ml_confidence) / 2 + 0.1)
                return rule_signal, combined_confidence, "ML + Rule Agreement"
            else:
                # Disagreement - use higher confidence
                if ml_confidence > rule_confidence:
                    return ml_signal, ml_confidence * 0.8, "ML Override"
                else:
                    return rule_signal, rule_confidence * 0.8, "Rule Override"
        
        elif rule_signal != 'HOLD' and ml_signal == 'HOLD':
            # Only rule-based has signal
            if rule_confidence >= TRADING_CONFIG['MIN_CONFIDENCE']:
                return rule_signal, rule_confidence * 0.9, "Rule Only"
            else:
                return 'HOLD', 0.0, "Insufficient Rule Confidence"
        
        elif rule_signal == 'HOLD' and ml_signal != 'HOLD':
            # Only ML has signal
            if ml_confidence >= TRADING_CONFIG['MIN_CONFIDENCE'] + 0.1:  # Higher threshold for ML-only
                return ml_signal, ml_confidence * 0.85, "ML Only"
            else:
                return 'HOLD', 0.0, "Insufficient ML Confidence"
        
        else:
            # Both are HOLD
            return 'HOLD', 0.0, "No Signal"
    
    def get_ml_model_info(self):
        """Get information about the loaded ML model"""
        try:
            if self.model is None:
                return "No ML model loaded"
            
            model_type = type(self.model).__name__
            feature_count = len(self.feature_columns) if self.feature_columns else 0
            
            return f"{model_type} with {feature_count} features"
            
        except Exception as e:
            return f"Error getting model info: {str(e)}"
