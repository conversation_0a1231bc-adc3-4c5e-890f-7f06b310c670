#!/usr/bin/env python3
"""
Test script to verify all optimized strategies work correctly
"""

import pandas as pd
import numpy as np
from strategy_engine import StrategyEngine
from utils import fetch_live_candles
from config import CURRENCY_PAIRS

def test_all_strategies():
    """Test all 10 strategies with real data"""
    print("🧪 Testing All Optimized Strategies...")
    
    # Initialize components
    strategy_engine = StrategyEngine()
    
    # Test with first 5 currency pairs
    test_pairs = CURRENCY_PAIRS[:5]
    
    results = {}
    
    for pair in test_pairs:
        print(f"\n📊 Testing {pair}...")
        
        try:
            # Fetch data
            df = fetch_live_candles(pair, 100, 'M1')
            
            if df is None or len(df) < 60:
                print(f"❌ {pair}: Insufficient data")
                continue
                
            # Test each strategy individually
            strategy_results = {}
            
            # Test Strategy 1
            s1_signal, s1_conf = strategy_engine.evaluate_strategy_1(df)
            strategy_results['S1'] = {'signal': s1_signal, 'confidence': s1_conf}
            
            # Test Strategy 2
            s2_signal, s2_conf = strategy_engine.evaluate_strategy_2(df)
            strategy_results['S2'] = {'signal': s2_signal, 'confidence': s2_conf}
            
            # Test Strategy 5
            s5_signal, s5_conf = strategy_engine.evaluate_strategy_5(df)
            strategy_results['S5'] = {'signal': s5_signal, 'confidence': s5_conf}
            
            # Test Strategy 6
            s6_signal, s6_conf = strategy_engine.evaluate_strategy_6(df)
            strategy_results['S6'] = {'signal': s6_signal, 'confidence': s6_conf}
            
            # Test Strategy 7
            s7_signal, s7_conf = strategy_engine.evaluate_strategy_7(df)
            strategy_results['S7'] = {'signal': s7_signal, 'confidence': s7_conf}
            
            # Test Strategy 9
            s9_signal, s9_conf = strategy_engine.evaluate_strategy_9(df)
            strategy_results['S9'] = {'signal': s9_signal, 'confidence': s9_conf}
            
            # Test Strategy 10
            s10_signal, s10_conf = strategy_engine.evaluate_strategy_10(df)
            strategy_results['S10'] = {'signal': s10_signal, 'confidence': s10_conf}
            
            results[pair] = strategy_results
            
            # Print results for this pair
            signals_found = 0
            for strategy, data in strategy_results.items():
                if data['signal'] != 0:
                    signal_type = 'BUY' if data['signal'] == 1 else 'SELL'
                    print(f"  ✅ {strategy}: {signal_type} ({data['confidence']:.1%})")
                    signals_found += 1
                else:
                    print(f"  ⚪ {strategy}: HOLD")
            
            print(f"  📈 Total signals: {signals_found}/7")
            
        except Exception as e:
            print(f"❌ {pair}: Error - {str(e)}")
            continue
    
    # Summary
    print(f"\n📋 SUMMARY:")
    print(f"Tested pairs: {len(results)}")
    
    strategy_signal_counts = {}
    for pair, pair_results in results.items():
        for strategy, data in pair_results.items():
            if strategy not in strategy_signal_counts:
                strategy_signal_counts[strategy] = 0
            if data['signal'] != 0:
                strategy_signal_counts[strategy] += 1
    
    print(f"\n🎯 Signal Generation by Strategy:")
    for strategy in ['S1', 'S2', 'S5', 'S6', 'S7', 'S9', 'S10']:
        count = strategy_signal_counts.get(strategy, 0)
        print(f"  {strategy}: {count}/{len(results)} pairs ({count/len(results)*100:.1f}%)")
    
    print(f"\n✅ Strategy optimization test completed!")

if __name__ == "__main__":
    test_all_strategies()
