#!/usr/bin/env python3
"""
Advanced Trading Bot with Quotex Integration
Features: Live trading, signal generation, and automated execution

pyquotex Setup Instructions:
1. Install pyquotex: pip install pyquotex
2. Install python-dotenv: pip install python-dotenv
3. Create .env file with:
   QUOTEX_EMAIL=<EMAIL>
   QUOTEX_PASSWORD=Uz2309##2309
4. Keep .env file secure and never commit to version control
"""

import os
import sys
import time
import asyncio
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Quotex Trading Pairs - Major and OTC pairs
QUOTEX_PAIRS = [
    # Major Forex Pairs
    "EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURJPY",
    "GBPJPY", "EURGBP", "AUDJPY", "EURAUD", "GBPAUD", "NZDCAD", "CADCHF", "CHFJPY",
    
    # Crypto Pairs
    "BTCUSD", "ETHUSD", "LTCUSD", "XRPUSD", "ADAUSD", "DOTUSD", "LINKUSD", "BCHUSD",
    "EOSUSD", "XLMUSD", "TRXUSD", "BNBUSD", "SOLUSD", "AVAXUSD", "MATICUSD", "DOGEUSD",
    
    # Commodities
    "XAUUSD", "XAGUSD", "WTIUSD", "BRENTUSD", "NATGAS", "COPPER", "PLATINUM", "PALLADIUM",
    
    # Indices
    "SPX500", "NAS100", "US30", "GER30", "UK100", "FRA40", "JPN225", "AUS200",
    "HK50", "EUSTX50", "ESP35", "ITA40", "NLD25", "SWI20", "CHINAA50", "IND50",
    
    # OTC Pairs (Available 24/7)
    "EURUSD_OTC", "GBPUSD_OTC", "USDJPY_OTC", "AUDUSD_OTC", "USDCAD_OTC", "USDCHF_OTC",
    "NZDUSD_OTC", "EURJPY_OTC", "GBPJPY_OTC", "EURGBP_OTC", "AUDJPY_OTC", "EURAUD_OTC",
    "XAUUSD_OTC", "XAGUSD_OTC", "WTIUSD_OTC", "BRENTUSD_OTC", "SPX500_OTC", "NAS100_OTC"
]

# Available timeframes
TIMEFRAMES = ["1m", "5m", "15m", "30m", "1h", "4h", "1d"]

# Available strategies
STRATEGIES = [
    "momentum_breakout", "support_resistance", "trend_following", "reversal_pattern",
    "volume_spike", "fibonacci_retracement", "bollinger_bands", "macd_divergence",
    "rsi_oversold", "moving_average_cross", "price_action", "harmonic_pattern"
]

def print_colored(text, color="white"):
    """Print colored text to console"""
    colors = {
        "red": "\033[91m",
        "green": "\033[92m",
        "yellow": "\033[93m",
        "blue": "\033[94m",
        "purple": "\033[95m",
        "cyan": "\033[96m",
        "white": "\033[97m",
        "reset": "\033[0m"
    }
    print(f"{colors.get(color, colors['white'])}{text}{colors['reset']}")

def display_pairs_in_columns(pairs, columns=4):
    """Display trading pairs in specified number of columns"""
    print_colored("\n📊 Available Trading Pairs:", "cyan")
    print_colored("=" * 80, "blue")
    
    for i in range(0, len(pairs), columns):
        row = pairs[i:i+columns]
        formatted_row = ""
        for j, pair in enumerate(row):
            formatted_row += f"{i+j+1:2d}. {pair:<15}"
        print_colored(formatted_row, "green")
    
    print_colored("=" * 80, "blue")

def select_pair():
    """Allow user to select a trading pair"""
    while True:
        display_pairs_in_columns(QUOTEX_PAIRS)
        
        try:
            choice = input(f"\nSelect pair (1-{len(QUOTEX_PAIRS)}) or type pair name: ").strip()
            
            # Check if user entered a number
            if choice.isdigit():
                index = int(choice) - 1
                if 0 <= index < len(QUOTEX_PAIRS):
                    selected_pair = QUOTEX_PAIRS[index]
                    print_colored(f"✅ Selected pair: {selected_pair}", "green")
                    return selected_pair
                else:
                    print_colored("❌ Invalid number. Please try again.", "red")
            
            # Check if user typed pair name
            elif choice.upper() in QUOTEX_PAIRS:
                selected_pair = choice.upper()
                print_colored(f"✅ Selected pair: {selected_pair}", "green")
                return selected_pair
            
            else:
                print_colored("❌ Invalid pair. Please try again.", "red")
                
        except ValueError:
            print_colored("❌ Invalid input. Please try again.", "red")

def select_timeframe():
    """Allow user to select a timeframe"""
    while True:
        print_colored("\n⏰ Available Timeframes:", "cyan")
        print_colored("=" * 40, "blue")
        
        for i, tf in enumerate(TIMEFRAMES, 1):
            print_colored(f"{i}. {tf}", "green")
        
        print_colored("=" * 40, "blue")
        
        try:
            choice = input(f"Select timeframe (1-{len(TIMEFRAMES)}): ").strip()
            
            if choice.isdigit():
                index = int(choice) - 1
                if 0 <= index < len(TIMEFRAMES):
                    selected_tf = TIMEFRAMES[index]
                    print_colored(f"✅ Selected timeframe: {selected_tf}", "green")
                    return selected_tf
                else:
                    print_colored("❌ Invalid number. Please try again.", "red")
            else:
                print_colored("❌ Invalid input. Please enter a number.", "red")
                
        except ValueError:
            print_colored("❌ Invalid input. Please try again.", "red")

def select_strategy():
    """Allow user to select a trading strategy"""
    while True:
        print_colored("\n🎯 Available Trading Strategies:", "cyan")
        print_colored("=" * 60, "blue")
        
        # Display strategies in 2 columns
        for i in range(0, len(STRATEGIES), 2):
            row = STRATEGIES[i:i+2]
            formatted_row = ""
            for j, strategy in enumerate(row):
                formatted_row += f"{i+j+1:2d}. {strategy:<25}"
            print_colored(formatted_row, "green")
        
        print_colored("=" * 60, "blue")
        
        try:
            choice = input(f"Select strategy (1-{len(STRATEGIES)}): ").strip()
            
            if choice.isdigit():
                index = int(choice) - 1
                if 0 <= index < len(STRATEGIES):
                    selected_strategy = STRATEGIES[index]
                    print_colored(f"✅ Selected strategy: {selected_strategy}", "green")
                    return selected_strategy
                else:
                    print_colored("❌ Invalid number. Please try again.", "red")
            else:
                print_colored("❌ Invalid input. Please enter a number.", "red")
                
        except ValueError:
            print_colored("❌ Invalid input. Please try again.", "red")

def select_account_type():
    """Allow user to select account type"""
    account_types = ["practice", "quotex_demo", "quotex_live"]
    
    while True:
        print_colored("\n💼 Account Type Selection:", "cyan")
        print_colored("=" * 50, "blue")
        print_colored("1. Practice (Signal display only)", "green")
        print_colored("2. Quotex Demo (Demo trading)", "yellow")
        print_colored("3. Quotex Live (Live trading)", "red")
        print_colored("=" * 50, "blue")
        
        try:
            choice = input("Select account type (1-3): ").strip()
            
            if choice in ['1', '2', '3']:
                selected_type = account_types[int(choice) - 1]
                
                if selected_type == "practice":
                    print_colored("✅ Practice mode selected - signals only", "green")
                elif selected_type == "quotex_demo":
                    print_colored("⚠️  Demo trading mode selected", "yellow")
                elif selected_type == "quotex_live":
                    print_colored("🚨 LIVE trading mode selected - REAL MONEY!", "red")
                    confirm = input("Type 'CONFIRM' to proceed with live trading: ").strip()
                    if confirm != 'CONFIRM':
                        print_colored("❌ Live trading cancelled", "yellow")
                        continue
                
                return selected_type
            else:
                print_colored("❌ Invalid choice. Please select 1, 2, or 3.", "red")
                
        except ValueError:
            print_colored("❌ Invalid input. Please try again.", "red")

def get_signal(pair, timeframe, strategy):
    """
    Simulate signal generation based on pair, timeframe, and strategy
    In a real implementation, this would contain actual technical analysis
    """
    import random
    
    # Simulate different signal probabilities based on strategy
    signal_weights = {
        "momentum_breakout": {"call": 0.35, "put": 0.35, "no signal": 0.30},
        "support_resistance": {"call": 0.30, "put": 0.30, "no signal": 0.40},
        "trend_following": {"call": 0.40, "put": 0.25, "no signal": 0.35},
        "reversal_pattern": {"call": 0.25, "put": 0.40, "no signal": 0.35},
        "volume_spike": {"call": 0.45, "put": 0.20, "no signal": 0.35},
        "fibonacci_retracement": {"call": 0.30, "put": 0.30, "no signal": 0.40},
        "bollinger_bands": {"call": 0.35, "put": 0.35, "no signal": 0.30},
        "macd_divergence": {"call": 0.40, "put": 0.30, "no signal": 0.30},
        "rsi_oversold": {"call": 0.50, "put": 0.15, "no signal": 0.35},
        "moving_average_cross": {"call": 0.35, "put": 0.35, "no signal": 0.30},
        "price_action": {"call": 0.30, "put": 0.30, "no signal": 0.40},
        "harmonic_pattern": {"call": 0.25, "put": 0.25, "no signal": 0.50}
    }
    
    weights = signal_weights.get(strategy, {"call": 0.33, "put": 0.33, "no signal": 0.34})
    signals = list(weights.keys())
    probabilities = list(weights.values())
    
    signal = random.choices(signals, weights=probabilities)[0]
    
    # Add some randomness for confidence
    if signal != "no signal":
        confidence = random.uniform(0.65, 0.95)
        return signal, confidence
    else:
        return signal, 0.0

def log_action(message, level="INFO"):
    """Log actions with timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    color_map = {
        "INFO": "cyan",
        "SUCCESS": "green",
        "WARNING": "yellow",
        "ERROR": "red",
        "SIGNAL": "purple"
    }
    color = color_map.get(level, "white")
    print_colored(f"[{timestamp}] {level}: {message}", color)

def connect_to_quotex(account_type):
    """
    Connect to Quotex using pyquotex
    Returns connection object or None if practice mode
    """
    if account_type == "practice":
        log_action("Practice mode - no Quotex connection needed", "INFO")
        return None
    
    try:
        # Import pyquotex (will fail if not installed)
        from pyquotex import Quotex
        
        # Get credentials from environment
        email = os.getenv("QUOTEX_EMAIL")
        password = os.getenv("QUOTEX_PASSWORD")
        
        if not email or not password:
            log_action("Quotex credentials not found in .env file", "ERROR")
            log_action("Please set QUOTEX_EMAIL and QUOTEX_PASSWORD in .env", "ERROR")
            return None
        
        log_action(f"Connecting to Quotex ({account_type})...", "INFO")
        
        # Initialize Quotex connection
        quotex = Quotex(email, password)
        
        # Set account type
        if account_type == "quotex_demo":
            quotex.change_balance("PRACTICE")  # Demo account
            log_action("Connected to Quotex Demo account", "SUCCESS")
        else:  # quotex_live
            quotex.change_balance("REAL")  # Live account
            log_action("Connected to Quotex Live account", "SUCCESS")
        
        return quotex
        
    except ImportError:
        log_action("pyquotex not installed. Run: pip install pyquotex", "ERROR")
        return None
    except Exception as e:
        log_action(f"Failed to connect to Quotex: {str(e)}", "ERROR")
        return None

async def place_trade(signal, pair, account_type, quotex_connection=None, amount=10):
    """
    Place trade based on signal and account type
    """
    if signal == "no signal":
        return False
    
    if account_type == "practice":
        log_action(f"PRACTICE SIGNAL: {signal.upper()} on {pair}", "SIGNAL")
        return True
    
    if quotex_connection is None:
        log_action("No Quotex connection available", "ERROR")
        return False
    
    try:
        # Convert signal to Quotex format
        direction = "call" if signal == "call" else "put"
        
        log_action(f"Placing {direction.upper()} trade on {pair} (${amount})", "WARNING")
        
        # Place trade using pyquotex
        result = await quotex_connection.buy(
            amount=amount,
            asset=pair,
            direction=direction,
            duration=60  # 1 minute
        )
        
        if result:
            log_action(f"Trade placed successfully: {direction.upper()} {pair}", "SUCCESS")
            return True
        else:
            log_action(f"Failed to place trade: {direction.upper()} {pair}", "ERROR")
            return False
            
    except Exception as e:
        log_action(f"Error placing trade: {str(e)}", "ERROR")
        return False

def calculate_next_scan_time():
    """Calculate the next scan time (58 seconds after current minute)"""
    now = datetime.now()
    next_minute = now.replace(second=58, microsecond=0)
    
    # If we're past 58 seconds, move to next minute
    if now.second >= 58:
        next_minute += timedelta(minutes=1)
    
    return next_minute

def is_valid_trade_time():
    """Check if current time is within 1 second after candle open"""
    now = datetime.now()
    seconds_after_minute = now.second
    
    # Valid trade window: 0-1 seconds after minute start
    return 0 <= seconds_after_minute <= 1

async def trading_loop(pair, timeframe, strategy, account_type, quotex_connection):
    """Main trading loop"""
    log_action("Starting trading loop...", "INFO")
    log_action(f"Pair: {pair}, Timeframe: {timeframe}, Strategy: {strategy}", "INFO")
    log_action(f"Account: {account_type}", "INFO")
    
    while True:
        try:
            # Calculate next scan time
            next_scan = calculate_next_scan_time()
            now = datetime.now()
            
            # Wait until scan time
            wait_seconds = (next_scan - now).total_seconds()
            if wait_seconds > 0:
                log_action(f"Next scan in {int(wait_seconds)} seconds at {next_scan.strftime('%H:%M:%S')}", "INFO")
                await asyncio.sleep(wait_seconds)
            
            # Fetch and analyze data (simulate)
            log_action(f"Analyzing {pair} with {strategy}...", "INFO")
            signal, confidence = get_signal(pair, timeframe, strategy)
            
            if signal != "no signal":
                log_action(f"Signal detected: {signal.upper()} (confidence: {confidence:.1%})", "SIGNAL")
                
                # Check if we're in valid trade window
                if is_valid_trade_time():
                    success = await place_trade(signal, pair, account_type, quotex_connection)
                    if success:
                        log_action("Trade executed successfully", "SUCCESS")
                    else:
                        log_action("Trade execution failed", "ERROR")
                else:
                    log_action("Signal detected but outside valid trade window - skipping", "WARNING")
            else:
                log_action("No signal detected", "INFO")
            
            # Wait a bit before next iteration
            await asyncio.sleep(5)
            
        except KeyboardInterrupt:
            log_action("Trading loop stopped by user", "WARNING")
            break
        except Exception as e:
            log_action(f"Error in trading loop: {str(e)}", "ERROR")
            await asyncio.sleep(10)  # Wait before retrying

async def main():
    """Main function"""
    print_colored("\n🚀 QUOTEX TRADING BOT", "cyan")
    print_colored("=" * 50, "blue")
    
    try:
        # User selections
        pair = select_pair()
        timeframe = select_timeframe()
        strategy = select_strategy()
        account_type = select_account_type()
        
        # Connect to Quotex if needed
        quotex_connection = connect_to_quotex(account_type)
        
        if account_type != "practice" and quotex_connection is None:
            print_colored("❌ Cannot proceed without Quotex connection", "red")
            return
        
        # Start trading loop
        print_colored("\n🎯 Starting trading bot...", "green")
        print_colored("Press Ctrl+C to stop", "yellow")
        
        await trading_loop(pair, timeframe, strategy, account_type, quotex_connection)
        
    except KeyboardInterrupt:
        print_colored("\n👋 Trading bot stopped by user", "yellow")
    except Exception as e:
        print_colored(f"\n❌ Error: {str(e)}", "red")

if __name__ == "__main__":
    asyncio.run(main())
