#!/usr/bin/env python3
"""
Strategy Engine for Trading Bot
Evaluates all four strategies and provides trading signals
"""

import pandas as pd
import numpy as np
from datetime import datetime
from config import STRATEGY_CONFIG, TRADING_CONFIG
from utils import print_colored, format_price

class StrategyEngine:
    def __init__(self):
        """Initialize the strategy engine"""
        self.strategies = STRATEGY_CONFIG

    def evaluate_strategy_1(self, df):
        """Evaluate Strategy 1: Breakout with Volume"""
        if len(df) < 50:
            return 0, 0.0
            
        try:
            # Get current candle data
            current = df.iloc[-1]
            prev = df.iloc[-2] if len(df) > 1 else current
            
            # Strategy 1 parameters
            window = 50
            min_gap = 0.00005
            max_gap = 0.0004
            
            # Calculate support/resistance
            resistance = df['high'].rolling(window=window).max().iloc[-1]
            support = df['low'].rolling(window=window).min().iloc[-1]
            
            # Current candle values
            open_ = current['open']
            high = current['high']
            low = current['low']
            close = current['close']
            volume = current['volume']
            prev_volume = prev['volume']
            
            # Calculate wicks and body
            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low
            
            # BUY signal conditions
            if (close > resistance and open_ < resistance and 
                close > open_ and volume >= prev_volume and 
                upper_wick < body * 0.3):
                return 1, 0.8  # BUY signal with high confidence
                
            # SELL signal conditions  
            elif (close < support and open_ > support and
                  close < open_ and volume >= prev_volume and
                  lower_wick < body * 0.3):
                return -1, 0.8  # SELL signal with high confidence
                
            return 0, 0.0  # No signal
            
        except Exception as e:
            print_colored(f"❌ Error in Strategy 1: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_2(self, df):
        """Evaluate Strategy 2: Order Block Strategy"""
        if len(df) < 70:
            return 0, 0.0
            
        try:
            current = df.iloc[-1]
            
            # Strategy 2 parameters
            lookback = 70
            trend_lookback = 20
            
            # Get current values
            current_rsi = current['rsi']
            volume = current['volume']
            
            # Calculate trend
            recent_closes = df['close'].tail(trend_lookback)
            trend = 1 if recent_closes.iloc[-1] > recent_closes.iloc[0] else -1
            
            # Volume analysis
            avg_vol = df['volume'].tail(10).mean()
            
            # Simplified order block detection
            high_levels = df['high'].tail(lookback).nlargest(5).values
            low_levels = df['low'].tail(lookback).nsmallest(5).values
            
            # Check for signals
            if (current['high'] > min(high_levels) and 
                current['close'] < min(high_levels) and
                current['close'] > current['open'] and
                volume <= avg_vol * 1.2 and current_rsi > 50):
                return -1, 0.7  # SELL signal
                
            elif (current['low'] < max(low_levels) and
                  current['close'] > max(low_levels) and
                  current['close'] < current['open'] and
                  volume <= avg_vol * 1.2 and current_rsi < 50):
                return 1, 0.7  # BUY signal
                
            return 0, 0.0
            
        except Exception as e:
            print_colored(f"❌ Error in Strategy 2: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_3(self, df):
        """Evaluate Strategy 3: Support/Resistance Rejection"""
        if len(df) < 50:
            return 0, 0.0
            
        try:
            current = df.iloc[-1]
            prev = df.iloc[-2] if len(df) > 1 else current
            
            # Strategy 3 parameters
            lookback = 50
            wick_ratio_threshold = 0.4
            
            # Calculate body and wicks
            open_ = current['open']
            high = current['high']
            low = current['low']
            close = current['close']
            volume = current['volume']
            prev_volume = prev['volume']
            
            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low
            
            # Find support/resistance levels
            resistance_levels = df['high'].tail(lookback).nlargest(3).values
            support_levels = df['low'].tail(lookback).nsmallest(3).values
            
            # Check resistance rejection (SELL signal)
            for resistance in resistance_levels:
                if (abs(high - resistance) <= 0.0001 and  # Touch resistance
                    close < open_ and  # Red candle
                    upper_wick >= wick_ratio_threshold * body and
                    volume <= prev_volume):
                    return -1, 0.75
                    
            # Check support bounce (BUY signal)
            for support in support_levels:
                if (abs(low - support) <= 0.0001 and  # Touch support
                    close > open_ and  # Green candle
                    lower_wick >= wick_ratio_threshold * body and
                    volume <= prev_volume):
                    return 1, 0.75
                    
            return 0, 0.0
            
        except Exception as e:
            print_colored(f"❌ Error in Strategy 3: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_4(self, df):
        """Evaluate Strategy 4: Trendline Break with Rejection"""
        if len(df) < 50:
            return 0, 0.0
            
        try:
            current = df.iloc[-1]
            prev = df.iloc[-2] if len(df) > 1 else current
            
            # Strategy 4 parameters
            lookback = 50
            min_wick_ratio = 0.3
            
            # Get current values
            open_ = current['open']
            high = current['high']
            low = current['low']
            close = current['close']
            volume = current['volume']
            prev_volume = prev['volume']
            
            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low
            
            # Simplified trendline detection
            recent_highs = df['high'].tail(lookback)
            recent_lows = df['low'].tail(lookback)
            
            # Check for uptrend trendline rejection
            if (close > open_ and  # Green candle
                upper_wick >= min_wick_ratio * body and
                volume <= prev_volume and
                recent_lows.iloc[-1] > recent_lows.iloc[-10]):  # Simple uptrend check
                return -1, 0.7
                
            # Check for downtrend trendline rejection  
            elif (close < open_ and  # Red candle
                  lower_wick >= min_wick_ratio * body and
                  volume <= prev_volume and
                  recent_highs.iloc[-1] < recent_highs.iloc[-10]):  # Simple downtrend check
                return 1, 0.7
                
            return 0, 0.0
            
        except Exception as e:
            print_colored(f"❌ Error in Strategy 4: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_all_strategies(self, df):
        """Evaluate all strategies and return combined signal"""
        signals = {}
        
        # Evaluate each strategy
        s1_signal, s1_conf = self.evaluate_strategy_1(df)
        s2_signal, s2_conf = self.evaluate_strategy_2(df)
        s3_signal, s3_conf = self.evaluate_strategy_3(df)
        s4_signal, s4_conf = self.evaluate_strategy_4(df)
        
        signals = {
            'S1': {'signal': s1_signal, 'confidence': s1_conf},
            'S2': {'signal': s2_signal, 'confidence': s2_conf},
            'S3': {'signal': s3_signal, 'confidence': s3_conf},
            'S4': {'signal': s4_signal, 'confidence': s4_conf}
        }
        
        # Find the strategy with highest confidence signal
        best_strategy = None
        best_signal = 0
        best_confidence = 0.0
        
        for strategy, data in signals.items():
            if data['signal'] != 0 and data['confidence'] > best_confidence:
                best_strategy = strategy
                best_signal = data['signal']
                best_confidence = data['confidence']
        
        # Return result
        if best_strategy and best_confidence >= TRADING_CONFIG['MIN_CONFIDENCE']:
            signal_name = 'BUY' if best_signal == 1 else 'SELL'
            return {
                'signal': signal_name,
                'confidence': best_confidence,
                'strategy': best_strategy,
                'price': df.iloc[-1]['close'],
                'all_signals': signals
            }
        else:
            return {
                'signal': 'HOLD',
                'confidence': 0.0,
                'strategy': None,
                'price': df.iloc[-1]['close'],
                'all_signals': signals
            }
