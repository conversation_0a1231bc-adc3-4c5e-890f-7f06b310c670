#!/usr/bin/env python3
"""
Strategy Engine for Trading Bot
Evaluates all four strategies and provides trading signals
"""

import pandas as pd
import numpy as np
from datetime import datetime
from config import STRATEGY_CONFIG, TRADING_CONFIG
from utils import print_colored, format_price

class StrategyEngine:
    def __init__(self):
        """Initialize the strategy engine"""
        self.strategies = STRATEGY_CONFIG

    def evaluate_strategy_1(self, df):
        """Evaluate Strategy 1: Breakout with Volume"""
        if len(df) < 50:
            return 0, 0.0
            
        try:
            # Get current candle data
            current = df.iloc[-1]
            prev = df.iloc[-2] if len(df) > 1 else current
            
            # Strategy 1 parameters
            window = 50
            min_gap = 0.00005
            max_gap = 0.0004
            
            # Calculate support/resistance
            resistance = df['high'].rolling(window=window).max().iloc[-1]
            support = df['low'].rolling(window=window).min().iloc[-1]
            
            # Current candle values
            open_ = current['open']
            high = current['high']
            low = current['low']
            close = current['close']
            volume = current['volume']
            prev_volume = prev['volume']
            
            # Calculate wicks and body
            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low
            
            # BUY signal conditions
            if (close > resistance and open_ < resistance and 
                close > open_ and volume >= prev_volume and 
                upper_wick < body * 0.3):
                return 1, 0.8  # BUY signal with high confidence
                
            # SELL signal conditions  
            elif (close < support and open_ > support and
                  close < open_ and volume >= prev_volume and
                  lower_wick < body * 0.3):
                return -1, 0.8  # SELL signal with high confidence
                
            return 0, 0.0  # No signal
            
        except Exception as e:
            print_colored(f"❌ Error in Strategy 1: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_2(self, df):
        """Strategy 2: PULLBACK ENTRY (High Accuracy Version)"""
        if len(df) < 15:
            return 0, 0.0

        try:
            # Calculate technical indicators
            df_copy = df.copy()

            # Calculate EMA 20
            df_copy['ema_20'] = df_copy['close'].ewm(span=20).mean()

            # Calculate RSI
            delta = df_copy['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df_copy['rsi'] = 100 - (100 / (1 + rs))

            current = df_copy.iloc[-1]
            prev = df_copy.iloc[-2]

            # Current candle data
            close = current['close']
            open_ = current['open']
            high = current['high']
            low = current['low']
            volume = current['volume']

            # Technical indicators
            ema_20 = current['ema_20'] if 'ema_20' in current and not pd.isna(current['ema_20']) else close
            rsi = current['rsi'] if 'rsi' in current and not pd.isna(current['rsi']) else 50

            # Candle structure analysis
            body = abs(close - open_)
            total_range = high - low
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low
            body_ratio = body / total_range if total_range > 0 else 0

            # Volume confirmation
            vol_avg = df_copy['volume'].tail(10).mean()
            volume_confirmed = volume > vol_avg * 1.2

            # Pullback detection - near EMA20
            pullback_tolerance = 0.008  # 0.8%
            near_ema = abs(close - ema_20) / ema_20 < pullback_tolerance if ema_20 > 0 else False

            # Trend strength analysis
            recent_closes = df_copy['close'].tail(6)
            uptrend_strength = sum(recent_closes.iloc[i] > recent_closes.iloc[i-1] for i in range(1, len(recent_closes)))
            downtrend_strength = sum(recent_closes.iloc[i] < recent_closes.iloc[i-1] for i in range(1, len(recent_closes)))

            # EMA trend confirmation
            ema_values = df_copy['ema_20'].tail(5)
            ema_rising = sum(ema_values.iloc[i] > ema_values.iloc[i-1] for i in range(1, len(ema_values))) >= 4
            ema_falling = sum(ema_values.iloc[i] < ema_values.iloc[i-1] for i in range(1, len(ema_values))) >= 4

            # Price position relative to EMA
            price_above_ema = close > ema_20 * 1.002  # 0.2% above EMA
            price_below_ema = close < ema_20 * 0.998  # 0.2% below EMA

            # Moderate candle requirements - less restrictive
            good_bullish = (close > open_ and body_ratio > 0.3)
            good_bearish = (close < open_ and body_ratio > 0.3)

            # BUY: Pullback entry in uptrend - relaxed conditions
            if (good_bullish and                # Good bullish candle
                near_ema and                    # Pullback to EMA
                uptrend_strength >= 3 and       # Moderate uptrend (3+ up candles)
                ema_rising and                  # EMA rising
                volume_confirmed and            # Volume confirmation
                40 <= rsi <= 80):               # Wider RSI range
                return 1, 0.88

            # SELL: Pullback entry in downtrend - relaxed conditions
            elif (good_bearish and             # Good bearish candle
                  near_ema and                 # Pullback to EMA
                  downtrend_strength >= 3 and  # Moderate downtrend (3+ down candles)
                  ema_falling and              # EMA falling
                  volume_confirmed and         # Volume confirmation
                  20 <= rsi <= 60):            # Wider RSI range
                return -1, 0.88

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 2: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_3(self, df):
        """Evaluate Strategy 3: Support/Resistance Rejection"""
        if len(df) < 50:
            return 0, 0.0
            
        try:
            current = df.iloc[-1]
            prev = df.iloc[-2] if len(df) > 1 else current
            
            # Strategy 3 parameters
            lookback = 50
            wick_ratio_threshold = 0.4
            
            # Calculate body and wicks
            open_ = current['open']
            high = current['high']
            low = current['low']
            close = current['close']
            volume = current['volume']
            prev_volume = prev['volume']
            
            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low
            
            # Find support/resistance levels
            resistance_levels = df['high'].tail(lookback).nlargest(3).values
            support_levels = df['low'].tail(lookback).nsmallest(3).values
            
            # Check resistance rejection (SELL signal)
            for resistance in resistance_levels:
                if (abs(high - resistance) <= 0.0001 and  # Touch resistance
                    close < open_ and  # Red candle
                    upper_wick >= wick_ratio_threshold * body and
                    volume <= prev_volume):
                    return -1, 0.75
                    
            # Check support bounce (BUY signal)
            for support in support_levels:
                if (abs(low - support) <= 0.0001 and  # Touch support
                    close > open_ and  # Green candle
                    lower_wick >= wick_ratio_threshold * body and
                    volume <= prev_volume):
                    return 1, 0.75
                    
            return 0, 0.0
            
        except Exception as e:
            print_colored(f"❌ Error in Strategy 3: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_4(self, df):
        """Evaluate Strategy 4: Trendline Break with Rejection"""
        if len(df) < 50:
            return 0, 0.0
            
        try:
            current = df.iloc[-1]
            prev = df.iloc[-2] if len(df) > 1 else current
            
            # Strategy 4 parameters
            lookback = 50
            min_wick_ratio = 0.3
            
            # Get current values
            open_ = current['open']
            high = current['high']
            low = current['low']
            close = current['close']
            volume = current['volume']
            prev_volume = prev['volume']
            
            body = abs(close - open_)
            upper_wick = high - max(open_, close)
            lower_wick = min(open_, close) - low
            
            # Simplified trendline detection
            recent_highs = df['high'].tail(lookback)
            recent_lows = df['low'].tail(lookback)
            
            # Check for uptrend trendline rejection
            if (close > open_ and  # Green candle
                upper_wick >= min_wick_ratio * body and
                volume <= prev_volume and
                recent_lows.iloc[-1] > recent_lows.iloc[-10]):  # Simple uptrend check
                return -1, 0.7
                
            # Check for downtrend trendline rejection  
            elif (close < open_ and  # Red candle
                  lower_wick >= min_wick_ratio * body and
                  volume <= prev_volume and
                  recent_highs.iloc[-1] < recent_highs.iloc[-10]):  # Simple downtrend check
                return 1, 0.7
                
            return 0, 0.0
            
        except Exception as e:
            print_colored(f"❌ Error in Strategy 4: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_5(self, df):
        """Strategy 5: Pre-Candle Momentum Strategy (75-85% Accuracy)
        Best for: Trending Markets (ADX > 25)
        Indicators: EMA 5 & EMA 9, RSI(14), Volume Spike
        """
        if len(df) < 50:
            return 0, 0.0

        try:
            # Calculate indicators
            df_copy = df.copy()

            # EMA 5 and EMA 9
            df_copy['ema5'] = df_copy['close'].ewm(span=5).mean()
            df_copy['ema9'] = df_copy['close'].ewm(span=9).mean()

            # RSI 14
            delta = df_copy['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df_copy['rsi'] = 100 - (100 / (1 + rs))

            # ADX for trend strength
            high_low = df_copy['high'] - df_copy['low']
            high_close = np.abs(df_copy['high'] - df_copy['close'].shift())
            low_close = np.abs(df_copy['low'] - df_copy['close'].shift())
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))

            plus_dm = np.where((df_copy['high'] - df_copy['high'].shift()) > (df_copy['low'].shift() - df_copy['low']),
                              np.maximum(df_copy['high'] - df_copy['high'].shift(), 0), 0)
            minus_dm = np.where((df_copy['low'].shift() - df_copy['low']) > (df_copy['high'] - df_copy['high'].shift()),
                               np.maximum(df_copy['low'].shift() - df_copy['low'], 0), 0)

            tr_smooth = pd.Series(true_range).rolling(window=14).mean()
            plus_di = 100 * (pd.Series(plus_dm).rolling(window=14).mean() / tr_smooth)
            minus_di = 100 * (pd.Series(minus_dm).rolling(window=14).mean() / tr_smooth)
            dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
            df_copy['adx'] = dx.rolling(window=14).mean()

            # Get current values
            current = df_copy.iloc[-1]
            prev = df_copy.iloc[-2]

            # Check if we're in a trending market (ADX > 25)
            if current['adx'] < 25:
                return 0, 0.0

            # Check trend direction
            uptrend = current['ema5'] > current['ema9']
            downtrend = current['ema5'] < current['ema9']

            # Volume spike check (current volume > 1.5x average of last 10)
            avg_volume = df_copy['volume'].tail(10).mean()
            volume_spike = current['volume'] > (avg_volume * 1.5)

            # RSI momentum check
            rsi_rising = current['rsi'] > prev['rsi']
            rsi_falling = current['rsi'] < prev['rsi']

            # Price near EMA5 check (within 0.1% of EMA5)
            price_near_ema5 = abs(current['close'] - current['ema5']) / current['close'] < 0.001

            # CALL Signal Logic
            if (uptrend and rsi_rising and current['rsi'] < 70 and
                price_near_ema5 and volume_spike):
                confidence = 0.75
                if current['rsi'] > 50:  # Strong momentum
                    confidence = 0.85
                return 1, confidence

            # PUT Signal Logic
            elif (downtrend and rsi_falling and current['rsi'] > 30 and
                  price_near_ema5 and volume_spike):
                confidence = 0.75
                if current['rsi'] < 50:  # Strong momentum
                    confidence = 0.85
                return -1, confidence

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 5: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_6(self, df):
        """Strategy 6: Pre-Candle Reversal Strategy (80-90% Accuracy in Ranges)
        Best for: Ranging Markets (ADX < 25, Bollinger Squeeze)
        Indicators: Bollinger Bands (20,2), Stochastic (5,3,3), Support/Resistance
        """
        if len(df) < 50:
            return 0, 0.0

        try:
            df_copy = df.copy()

            # Bollinger Bands (20, 2)
            df_copy['bb_middle'] = df_copy['close'].rolling(window=20).mean()
            bb_std = df_copy['close'].rolling(window=20).std()
            df_copy['bb_upper'] = df_copy['bb_middle'] + (bb_std * 2)
            df_copy['bb_lower'] = df_copy['bb_middle'] - (bb_std * 2)

            # Stochastic (5,3,3)
            low_min = df_copy['low'].rolling(window=5).min()
            high_max = df_copy['high'].rolling(window=5).max()
            k_percent = 100 * ((df_copy['close'] - low_min) / (high_max - low_min))
            df_copy['stoch_k'] = k_percent.rolling(window=3).mean()
            df_copy['stoch_d'] = df_copy['stoch_k'].rolling(window=3).mean()

            # ADX for trend strength (same calculation as Strategy 5)
            high_low = df_copy['high'] - df_copy['low']
            high_close = np.abs(df_copy['high'] - df_copy['close'].shift())
            low_close = np.abs(df_copy['low'] - df_copy['close'].shift())
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))

            plus_dm = np.where((df_copy['high'] - df_copy['high'].shift()) > (df_copy['low'].shift() - df_copy['low']),
                              np.maximum(df_copy['high'] - df_copy['high'].shift(), 0), 0)
            minus_dm = np.where((df_copy['low'].shift() - df_copy['low']) > (df_copy['high'] - df_copy['high'].shift()),
                               np.maximum(df_copy['low'].shift() - df_copy['low'], 0), 0)

            tr_smooth = pd.Series(true_range).rolling(window=14).mean()
            plus_di = 100 * (pd.Series(plus_dm).rolling(window=14).mean() / tr_smooth)
            minus_di = 100 * (pd.Series(minus_dm).rolling(window=14).mean() / tr_smooth)
            dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
            df_copy['adx'] = dx.rolling(window=14).mean()

            # Support and Resistance levels (last 20 candles)
            recent_data = df_copy.tail(20)
            resistance = recent_data['high'].max()
            support = recent_data['low'].min()

            current = df_copy.iloc[-1]
            prev = df_copy.iloc[-2]

            # Only trade in ranging markets (ADX < 25)
            if current['adx'] >= 25:
                return 0, 0.0

            # Check for Bollinger Band squeeze (bands are tight)
            bb_width = (current['bb_upper'] - current['bb_lower']) / current['bb_middle']
            if bb_width > 0.04:  # Not squeezed enough
                return 0, 0.0

            # Check candlestick patterns
            body = abs(current['close'] - current['open'])
            upper_wick = current['high'] - max(current['close'], current['open'])
            lower_wick = min(current['close'], current['open']) - current['low']

            # Bearish patterns
            bearish_engulfing = (prev['close'] > prev['open'] and current['close'] < current['open'] and
                               current['open'] > prev['close'] and current['close'] < prev['open'])
            pin_bar_top = upper_wick > (body * 2) and lower_wick < (body * 0.5)

            # Bullish patterns
            bullish_engulfing = (prev['close'] < prev['open'] and current['close'] > current['open'] and
                               current['open'] < prev['close'] and current['close'] > prev['open'])
            hammer = lower_wick > (body * 2) and upper_wick < (body * 0.5)

            # PUT Signal: Price at upper BB + Stochastic overbought + bearish pattern
            if (current['close'] >= current['bb_upper'] * 0.99 and  # Near upper band
                current['stoch_k'] > 80 and  # Overbought
                (bearish_engulfing or pin_bar_top) and  # Bearish pattern
                current['close'] >= resistance * 0.999):  # Near resistance
                confidence = 0.80
                if current['stoch_k'] > 90:  # Very overbought
                    confidence = 0.90
                return -1, confidence

            # CALL Signal: Price at lower BB + Stochastic oversold + bullish pattern
            elif (current['close'] <= current['bb_lower'] * 1.01 and  # Near lower band
                  current['stoch_k'] < 20 and  # Oversold
                  (bullish_engulfing or hammer) and  # Bullish pattern
                  current['close'] <= support * 1.001):  # Near support
                confidence = 0.80
                if current['stoch_k'] < 10:  # Very oversold
                    confidence = 0.90
                return 1, confidence

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 6: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_7(self, df):
        """Strategy 7: Pre-Candle Breakout Strategy (75-85% Accuracy)
        Best for: High Volatility (News, Breakouts)
        Indicators: Previous Candle High/Low, Volume Surge, MACD Cross (12,26,9)
        """
        if len(df) < 50:
            return 0, 0.0

        try:
            df_copy = df.copy()

            # MACD (12, 26, 9)
            exp1 = df_copy['close'].ewm(span=12).mean()
            exp2 = df_copy['close'].ewm(span=26).mean()
            df_copy['macd'] = exp1 - exp2
            df_copy['macd_signal'] = df_copy['macd'].ewm(span=9).mean()
            df_copy['macd_histogram'] = df_copy['macd'] - df_copy['macd_signal']

            # Bollinger Bands for volatility check
            df_copy['bb_middle'] = df_copy['close'].rolling(window=20).mean()
            bb_std = df_copy['close'].rolling(window=20).std()
            df_copy['bb_upper'] = df_copy['bb_middle'] + (bb_std * 2)
            df_copy['bb_lower'] = df_copy['bb_middle'] - (bb_std * 2)

            current = df_copy.iloc[-1]
            prev = df_copy.iloc[-2]
            prev2 = df_copy.iloc[-3]

            # Check for sufficient volatility (Bollinger Bands not too tight)
            bb_width = (current['bb_upper'] - current['bb_lower']) / current['bb_middle']
            if bb_width < 0.02:  # Too low volatility
                return 0, 0.0

            # Previous candle high/low levels
            prev_high = prev['high']
            prev_low = prev['low']

            # Volume surge check (current volume > 2x average of last 10)
            avg_volume = df_copy['volume'].tail(10).mean()
            volume_surge = current['volume'] > (avg_volume * 2.0)

            # MACD cross detection
            macd_bullish_cross = (current['macd'] > current['macd_signal'] and
                                 prev['macd'] <= prev['macd_signal'])
            macd_bearish_cross = (current['macd'] < current['macd_signal'] and
                                 prev['macd'] >= prev['macd_signal'])

            # Price hovering near previous levels (within 0.05% of previous high/low)
            near_prev_high = abs(current['close'] - prev_high) / current['close'] < 0.0005
            near_prev_low = abs(current['close'] - prev_low) / current['close'] < 0.0005

            # CALL Signal: Price near previous high + volume surge + MACD bullish cross
            if (near_prev_high and volume_surge and macd_bullish_cross and
                current['close'] > prev_high * 0.999):  # Just above previous high
                confidence = 0.75
                if current['macd_histogram'] > prev['macd_histogram']:  # Strengthening momentum
                    confidence = 0.85
                return 1, confidence

            # PUT Signal: Price near previous low + volume surge + MACD bearish cross
            elif (near_prev_low and volume_surge and macd_bearish_cross and
                  current['close'] < prev_low * 1.001):  # Just below previous low
                confidence = 0.75
                if current['macd_histogram'] < prev['macd_histogram']:  # Strengthening momentum
                    confidence = 0.85
                return -1, confidence

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 7: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_8(self, df):
        """Strategy 8: Pre-Candle Fakeout Strategy (85%+ Accuracy in Choppy Markets)
        Best for: False Breakout Traps
        Indicators: Fibonacci Retracement (38.2%, 61.8%), RSI Divergence
        """
        if len(df) < 50:
            return 0, 0.0

        try:
            df_copy = df.copy()

            # RSI for divergence detection
            delta = df_copy['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df_copy['rsi'] = 100 - (100 / (1 + rs))

            # Find recent swing high and low for Fibonacci levels
            recent_data = df_copy.tail(20)
            swing_high = recent_data['high'].max()
            swing_low = recent_data['low'].min()

            # Calculate Fibonacci retracement levels
            fib_range = swing_high - swing_low
            fib_382 = swing_high - (fib_range * 0.382)
            fib_618 = swing_high - (fib_range * 0.618)

            current = df_copy.iloc[-1]
            prev = df_copy.iloc[-2]
            prev2 = df_copy.iloc[-3]

            # Support and resistance levels (last 30 candles for better detection)
            extended_data = df_copy.tail(30)
            resistance_level = extended_data['high'].max()
            support_level = extended_data['low'].min()

            # RSI divergence detection
            # Bullish divergence: Price makes lower low, RSI makes higher low
            price_lower_low = current['low'] < prev2['low'] and prev['low'] < prev2['low']
            rsi_higher_low = current['rsi'] > prev2['rsi'] and prev['rsi'] > prev2['rsi']
            bullish_divergence = price_lower_low and rsi_higher_low

            # Bearish divergence: Price makes higher high, RSI makes lower high
            price_higher_high = current['high'] > prev2['high'] and prev['high'] > prev2['high']
            rsi_lower_high = current['rsi'] < prev2['rsi'] and prev['rsi'] < prev2['rsi']
            bearish_divergence = price_higher_high and rsi_lower_high

            # Fakeout detection: Brief break of level but quick reversal
            # Check if price briefly broke a level in previous candles but is now reversing

            # Fake breakdown detection (for CALL signals)
            fake_breakdown = (prev['low'] < support_level * 0.999 and  # Previous candle broke support
                             current['close'] > support_level and  # Current candle back above support
                             bullish_divergence)  # RSI shows bullish divergence

            # Fake breakout detection (for PUT signals)
            fake_breakout = (prev['high'] > resistance_level * 1.001 and  # Previous candle broke resistance
                            current['close'] < resistance_level and  # Current candle back below resistance
                            bearish_divergence)  # RSI shows bearish divergence

            # Check if price is near Fibonacci levels for confirmation
            near_fib_382 = abs(current['close'] - fib_382) / current['close'] < 0.001
            near_fib_618 = abs(current['close'] - fib_618) / current['close'] < 0.001

            # CALL Signal: Fake breakdown + bullish divergence + Fibonacci confirmation
            if fake_breakdown and (near_fib_382 or near_fib_618):
                confidence = 0.85
                if near_fib_618:  # Stronger level
                    confidence = 0.90
                return 1, confidence

            # PUT Signal: Fake breakout + bearish divergence + Fibonacci confirmation
            elif fake_breakout and (near_fib_382 or near_fib_618):
                confidence = 0.85
                if near_fib_618:  # Stronger level
                    confidence = 0.90
                return -1, confidence

            # Alternative fakeout detection without strict level breaks
            # Look for RSI divergence near key levels
            elif (bullish_divergence and current['close'] <= support_level * 1.002 and
                  (near_fib_382 or near_fib_618)):
                return 1, 0.80

            elif (bearish_divergence and current['close'] >= resistance_level * 0.998 and
                  (near_fib_382 or near_fib_618)):
                return -1, 0.80

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 8: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_9(self, df):
        """Strategy 9: Ranging Market Strategy (75-85% Accuracy)
        Best for: Sideways Markets (Support/Resistance + RSI + Price Action)
        Indicators: Support/Resistance Zones, RSI(14), EMA 20, Price Action Patterns
        """
        if len(df) < 50:
            return 0, 0.0

        try:
            df_copy = df.copy()

            # Calculate RSI(14)
            delta = df_copy['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df_copy['rsi'] = 100 - (100 / (1 + rs))

            # Calculate EMA 20
            df_copy['ema20'] = df_copy['close'].ewm(span=20).mean()

            # Calculate EMA slope to detect flat EMA (ranging market)
            ema_values = df_copy['ema20'].tail(10).values
            if len(ema_values) >= 2:
                x = np.arange(len(ema_values))
                slope = np.polyfit(x, ema_values, 1)[0]
                ema_slope_degrees = np.degrees(np.arctan(slope / ema_values[-1] * 1000))  # Convert to degrees
            else:
                ema_slope_degrees = 0

            # ADX calculation for trend strength
            high_low = df_copy['high'] - df_copy['low']
            high_close = np.abs(df_copy['high'] - df_copy['close'].shift())
            low_close = np.abs(df_copy['low'] - df_copy['close'].shift())
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))

            plus_dm = np.where((df_copy['high'].diff() > df_copy['low'].diff().abs()) &
                              (df_copy['high'].diff() > 0), df_copy['high'].diff(), 0)
            minus_dm = np.where((df_copy['low'].diff().abs() > df_copy['high'].diff()) &
                               (df_copy['low'].diff() < 0), df_copy['low'].diff().abs(), 0)

            atr = pd.Series(true_range).rolling(window=14).mean()
            plus_di = 100 * (pd.Series(plus_dm).rolling(window=14).mean() / atr)
            minus_di = 100 * (pd.Series(minus_dm).rolling(window=14).mean() / atr)
            dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
            df_copy['adx'] = dx.rolling(window=14).mean()

            current = df_copy.iloc[-1]
            prev = df_copy.iloc[-2] if len(df_copy) > 1 else current

            # Market type detection
            market_type = 'unclear'
            if abs(ema_slope_degrees) < 3 and current['adx'] < 20:
                market_type = 'ranging'
            elif abs(ema_slope_degrees) >= 3 and current['adx'] > 20:
                market_type = 'trending'

            # Only proceed if market is ranging
            if market_type != 'ranging':
                return 0, 0.0

            # Find support and resistance zones (last 50 candles)
            lookback = min(50, len(df_copy))
            recent_data = df_copy.tail(lookback)

            # Support levels (lowest lows)
            support_levels = recent_data['low'].nsmallest(3).values
            # Resistance levels (highest highs)
            resistance_levels = recent_data['high'].nlargest(3).values

            # Current candle analysis
            open_price = current['open']
            high_price = current['high']
            low_price = current['low']
            close_price = current['close']

            body = abs(close_price - open_price)
            upper_wick = high_price - max(open_price, close_price)
            lower_wick = min(open_price, close_price) - low_price
            total_range = high_price - low_price

            # Price action pattern detection
            # Bullish patterns
            bullish_pin_bar = (lower_wick > body * 2 and upper_wick < body * 0.5 and close_price > open_price)
            hammer = (lower_wick > body * 2 and upper_wick < body * 0.3)
            bullish_engulfing = (prev['close'] < prev['open'] and close_price > open_price and
                               open_price < prev['close'] and close_price > prev['open'])

            # Bearish patterns
            bearish_pin_bar = (upper_wick > body * 2 and lower_wick < body * 0.5 and close_price < open_price)
            shooting_star = (upper_wick > body * 2 and lower_wick < body * 0.3)
            bearish_engulfing = (prev['close'] > prev['open'] and close_price < open_price and
                               open_price > prev['close'] and close_price < prev['open'])

            # Check proximity to support/resistance levels
            near_support = False
            near_resistance = False

            for support in support_levels:
                if abs(close_price - support) / close_price < 0.002:  # Within 0.2%
                    near_support = True
                    break

            for resistance in resistance_levels:
                if abs(close_price - resistance) / close_price < 0.002:  # Within 0.2%
                    near_resistance = True
                    break

            # CALL (BUY) Signal Conditions
            if (near_support and current['rsi'] < 30 and
                (bullish_pin_bar or hammer or bullish_engulfing) and
                abs(ema_slope_degrees) < 3):
                confidence = 0.75
                if current['rsi'] < 25:  # Very oversold
                    confidence = 0.85
                return 1, confidence

            # PUT (SELL) Signal Conditions
            elif (near_resistance and current['rsi'] > 70 and
                  (bearish_pin_bar or shooting_star or bearish_engulfing) and
                  abs(ema_slope_degrees) < 3):
                confidence = 0.75
                if current['rsi'] > 75:  # Very overbought
                    confidence = 0.85
                return -1, confidence

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 9: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_strategy_10(self, df):
        """Strategy 10: Trending Market Strategy (78-88% Accuracy)
        Best for: Trending Markets (EMA + MACD + Pullback Confirmation)
        Indicators: EMA 20/50, MACD(12,26,9), ADX, Support/Resistance
        """
        if len(df) < 60:
            return 0, 0.0

        try:
            df_copy = df.copy()

            # Calculate EMAs
            df_copy['ema20'] = df_copy['close'].ewm(span=20).mean()
            df_copy['ema50'] = df_copy['close'].ewm(span=50).mean()

            # Calculate MACD (12, 26, 9)
            exp1 = df_copy['close'].ewm(span=12).mean()
            exp2 = df_copy['close'].ewm(span=26).mean()
            df_copy['macd'] = exp1 - exp2
            df_copy['macd_signal'] = df_copy['macd'].ewm(span=9).mean()
            df_copy['macd_histogram'] = df_copy['macd'] - df_copy['macd_signal']

            # Calculate EMA slope to detect trend
            ema20_values = df_copy['ema20'].tail(10).values
            if len(ema20_values) >= 2:
                x = np.arange(len(ema20_values))
                slope = np.polyfit(x, ema20_values, 1)[0]
                ema_slope_degrees = np.degrees(np.arctan(slope / ema20_values[-1] * 1000))
            else:
                ema_slope_degrees = 0

            # ADX calculation for trend strength
            high_low = df_copy['high'] - df_copy['low']
            high_close = np.abs(df_copy['high'] - df_copy['close'].shift())
            low_close = np.abs(df_copy['low'] - df_copy['close'].shift())
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))

            plus_dm = np.where((df_copy['high'].diff() > df_copy['low'].diff().abs()) &
                              (df_copy['high'].diff() > 0), df_copy['high'].diff(), 0)
            minus_dm = np.where((df_copy['low'].diff().abs() > df_copy['high'].diff()) &
                               (df_copy['low'].diff() < 0), df_copy['low'].diff().abs(), 0)

            atr = pd.Series(true_range).rolling(window=14).mean()
            plus_di = 100 * (pd.Series(plus_dm).rolling(window=14).mean() / atr)
            minus_di = 100 * (pd.Series(minus_dm).rolling(window=14).mean() / atr)
            dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
            df_copy['adx'] = dx.rolling(window=14).mean()

            current = df_copy.iloc[-1]
            prev = df_copy.iloc[-2] if len(df_copy) > 1 else current

            # Market type detection
            market_type = 'unclear'
            if abs(ema_slope_degrees) >= 3 and current['adx'] > 20:
                market_type = 'trending'
            elif abs(ema_slope_degrees) < 3 and current['adx'] < 20:
                market_type = 'ranging'

            # Only proceed if market is trending
            if market_type != 'trending':
                return 0, 0.0

            # Find support and resistance levels
            lookback = min(50, len(df_copy))
            recent_data = df_copy.tail(lookback)
            support_levels = recent_data['low'].nsmallest(3).values
            resistance_levels = recent_data['high'].nlargest(3).values

            # Current price analysis
            close_price = current['close']
            open_price = current['open']

            # MACD cross detection
            macd_bullish_cross = (current['macd'] > current['macd_signal'] and
                                 prev['macd'] <= prev['macd_signal'])
            macd_bearish_cross = (current['macd'] < current['macd_signal'] and
                                 prev['macd'] >= prev['macd_signal'])

            # Price pullback to EMA20 detection (within 0.1% of EMA20)
            near_ema20 = abs(close_price - current['ema20']) / close_price < 0.001

            # Candle pattern detection
            bullish_candle = close_price > open_price
            bearish_candle = close_price < open_price

            # Check proximity to support/resistance levels
            near_resistance = any(abs(close_price - resistance) / close_price < 0.002
                                for resistance in resistance_levels)
            near_support = any(abs(close_price - support) / close_price < 0.002
                             for support in support_levels)

            # CALL (BUY) Signal - Uptrend conditions
            if (current['ema20'] > current['ema50'] and  # Uptrend
                macd_bullish_cross and  # MACD bullish cross
                near_ema20 and  # Price near EMA20 (pullback)
                bullish_candle and  # Bullish candle formation
                not near_resistance and  # No resistance nearby
                current['adx'] > 20):  # Strong trend
                confidence = 0.78
                if current['macd_histogram'] > prev['macd_histogram']:  # Strengthening momentum
                    confidence = 0.88
                return 1, confidence

            # PUT (SELL) Signal - Downtrend conditions
            elif (current['ema20'] < current['ema50'] and  # Downtrend
                  macd_bearish_cross and  # MACD bearish cross
                  near_ema20 and  # Price near EMA20 (pullback)
                  bearish_candle and  # Bearish candle formation
                  not near_support and  # No support nearby
                  current['adx'] > 20):  # Strong trend
                confidence = 0.78
                if current['macd_histogram'] < prev['macd_histogram']:  # Strengthening momentum
                    confidence = 0.88
                return -1, confidence

            return 0, 0.0

        except Exception as e:
            print_colored(f"❌ Error in Strategy 10: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_all_strategies(self, df, selected_strategies=None):
        """Evaluate all strategies and return combined signal"""
        if selected_strategies is None:
            selected_strategies = ['S1', 'S2', 'S3', 'S4', 'S5', 'S6', 'S7', 'S8', 'S9', 'S10']

        signals = {}

        # Evaluate each strategy if selected
        if 'S1' in selected_strategies:
            s1_signal, s1_conf = self.evaluate_strategy_1(df)
            signals['S1'] = {'signal': s1_signal, 'confidence': s1_conf}

        if 'S2' in selected_strategies:
            s2_signal, s2_conf = self.evaluate_strategy_2(df)
            signals['S2'] = {'signal': s2_signal, 'confidence': s2_conf}

        if 'S3' in selected_strategies:
            s3_signal, s3_conf = self.evaluate_strategy_3(df)
            signals['S3'] = {'signal': s3_signal, 'confidence': s3_conf}

        if 'S4' in selected_strategies:
            s4_signal, s4_conf = self.evaluate_strategy_4(df)
            signals['S4'] = {'signal': s4_signal, 'confidence': s4_conf}

        if 'S5' in selected_strategies:
            s5_signal, s5_conf = self.evaluate_strategy_5(df)
            signals['S5'] = {'signal': s5_signal, 'confidence': s5_conf}

        if 'S6' in selected_strategies:
            s6_signal, s6_conf = self.evaluate_strategy_6(df)
            signals['S6'] = {'signal': s6_signal, 'confidence': s6_conf}

        if 'S7' in selected_strategies:
            s7_signal, s7_conf = self.evaluate_strategy_7(df)
            signals['S7'] = {'signal': s7_signal, 'confidence': s7_conf}

        if 'S8' in selected_strategies:
            s8_signal, s8_conf = self.evaluate_strategy_8(df)
            signals['S8'] = {'signal': s8_signal, 'confidence': s8_conf}

        if 'S9' in selected_strategies:
            s9_signal, s9_conf = self.evaluate_strategy_9(df)
            signals['S9'] = {'signal': s9_signal, 'confidence': s9_conf}

        if 'S10' in selected_strategies:
            s10_signal, s10_conf = self.evaluate_strategy_10(df)
            signals['S10'] = {'signal': s10_signal, 'confidence': s10_conf}
        
        # Find the strategy with highest confidence signal
        best_strategy = None
        best_signal = 0
        best_confidence = 0.0
        
        for strategy, data in signals.items():
            if data['signal'] != 0 and data['confidence'] > best_confidence:
                best_strategy = strategy
                best_signal = data['signal']
                best_confidence = data['confidence']
        
        # Return result
        if best_strategy and best_confidence >= TRADING_CONFIG['MIN_CONFIDENCE']:
            signal_name = 'BUY' if best_signal == 1 else 'SELL'
            return {
                'signal': signal_name,
                'confidence': best_confidence,
                'strategy': best_strategy,
                'price': df.iloc[-1]['close'],
                'all_signals': signals
            }
        else:
            return {
                'signal': 'HOLD',
                'confidence': 0.0,
                'strategy': None,
                'price': df.iloc[-1]['close'],
                'all_signals': signals
            }

    def calculate_atr(self, df, period=14):
        """Calculate Average True Range"""
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        return tr.rolling(period).mean()

    def calculate_rsi_custom(self, df, period=14):
        """Calculate RSI if not available"""
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)

        avg_gain = gain.rolling(period).mean()
        avg_loss = loss.rolling(period).mean()

        rs = avg_gain / avg_loss
        return 100 - (100 / (1 + rs))
