#!/usr/bin/env python3
"""
Final system test to verify all components work together
"""

from utils import print_colored, print_header, select_currency_pairs, select_timeframe, select_strategies
from strategy_engine import StrategyEngine
from live_trading_bot import LiveTradingBot
from config import STRATEGY_CONFIG
import pandas as pd
import numpy as np

def test_system_integration():
    """Test the complete system integration"""
    print_header("🔧 FINAL SYSTEM TEST")
    print_colored("Testing complete trading bot system integration...", "INFO")
    print()
    
    try:
        # Test 1: Configuration loading
        print_colored("1. Testing configuration loading...", "INFO")
        assert len(STRATEGY_CONFIG) == 8, f"Expected 8 strategies, got {len(STRATEGY_CONFIG)}"
        for i in range(1, 9):
            assert f"S{i}" in STRATEGY_CONFIG, f"Strategy S{i} not found in config"
        print_colored("✅ Configuration loaded successfully", "SUCCESS")
        print()
        
        # Test 2: Strategy Engine
        print_colored("2. Testing strategy engine...", "INFO")
        strategy_engine = StrategyEngine()
        
        # Create test data
        test_data = pd.DataFrame({
            'open': [1.1000] * 50,
            'high': [1.1005] * 50,
            'low': [1.0995] * 50,
            'close': [1.1002] * 50,
            'volume': [1000] * 50
        })
        
        # Test all strategies
        all_results = strategy_engine.evaluate_all_strategies(test_data)
        assert len(all_results) > 0, "No strategy results returned"
        print_colored("✅ Strategy engine working correctly", "SUCCESS")
        
        # Test selected strategies
        selected_results = strategy_engine.evaluate_all_strategies(test_data, ['S1', 'S5', 'S8'])
        assert len(selected_results) <= 3, "Too many results for selected strategies"
        print_colored("✅ Strategy selection working correctly", "SUCCESS")
        print()
        
        # Test 3: Live Trading Bot initialization
        print_colored("3. Testing live trading bot initialization...", "INFO")
        bot = LiveTradingBot(
            selected_pairs=['EUR_USD'],
            timeframe='1min',
            selected_strategies=['S1', 'S5', 'S6', 'S8']
        )
        assert bot.selected_pairs == ['EUR_USD'], "Pairs not set correctly"
        assert bot.timeframe == '1min', "Timeframe not set correctly"
        assert bot.selected_strategies == ['S1', 'S5', 'S6', 'S8'], "Strategies not set correctly"
        print_colored("✅ Live trading bot initialized successfully", "SUCCESS")
        print()
        
        # Test 4: Utility functions (mock test)
        print_colored("4. Testing utility functions...", "INFO")
        # These functions require user input, so we just test they exist
        assert callable(select_currency_pairs), "select_currency_pairs function not found"
        assert callable(select_timeframe), "select_timeframe function not found"
        assert callable(select_strategies), "select_strategies function not found"
        print_colored("✅ Utility functions available", "SUCCESS")
        print()
        
        print_header("🎉 SYSTEM TEST RESULTS")
        print_colored("All system components are working correctly!", "SUCCESS")
        print_colored("✅ Configuration: 8 strategies loaded", "SUCCESS")
        print_colored("✅ Strategy Engine: All strategies functional", "SUCCESS")
        print_colored("✅ Live Trading Bot: Initialization successful", "SUCCESS")
        print_colored("✅ Utility Functions: All functions available", "SUCCESS")
        print()
        print_colored("🚀 System is ready for live trading!", "SUCCESS")
        
        return True
        
    except Exception as e:
        print_colored(f"❌ System test failed: {str(e)}", "ERROR")
        return False

def display_system_summary():
    """Display a summary of the complete system"""
    print_header("📋 TRADING BOT SYSTEM SUMMARY")
    
    print_colored("🎯 AVAILABLE FEATURES:", "INFO")
    print_colored("• Rule-based trading with 8 strategies (S1-S8)", "SUCCESS")
    print_colored("• Advanced signal generator", "SUCCESS")
    print_colored("• Multiple timeframe support (1min, 2min, 5min, 10min, 15min, 30min, 1hour)", "SUCCESS")
    print_colored("• Multiple currency pair selection", "SUCCESS")
    print_colored("• Strategy selection (individual or combinations)", "SUCCESS")
    print()
    
    print_colored("📊 AVAILABLE STRATEGIES:", "INFO")
    for strategy_id, strategy_info in STRATEGY_CONFIG.items():
        if strategy_info.get('enabled', True):
            name = strategy_info.get('name', f'Strategy {strategy_id}')
            accuracy = strategy_info.get('accuracy', 'N/A')
            print_colored(f"• {strategy_id}: {name} ({accuracy})", "SUCCESS")
    print()
    
    print_colored("🚀 TO START TRADING:", "INFO")
    print_colored("Run: python trading_bot_launcher.py", "WARNING")
    print_colored("Select option 1 for Rule-Based Trading", "WARNING")
    print()

if __name__ == "__main__":
    # Run system test
    success = test_system_integration()
    
    if success:
        print()
        display_system_summary()
    else:
        print_colored("❌ System test failed. Please check the errors above.", "ERROR")
