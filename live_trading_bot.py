#!/usr/bin/env python3
"""
Live Trading Bot with Oanda API Integration
Fetches live market data and provides trading signals every minute
"""

import time
import threading
from datetime import datetime, timedelta
import pandas as pd
from strategy_engine import StrategyEngine
from utils import (
    fetch_live_candles, get_current_time_info, get_timeframe_time_info, print_colored,
    print_header, print_table_row, format_signal_output, validate_pair,
    print_signal_table_header, print_signal_row, select_strategies
)
from config import CURRENCY_PAIRS, TRADING_CONFIG, DISPLAY_CONFIG, TIMEFRAME_CONFIG

class LiveTradingBot:
    def __init__(self, selected_pairs=None, timeframe="1min", selected_strategies=None):
        """Initialize the live trading bot"""
        self.strategy_engine = StrategyEngine()
        self.running = False
        self.pairs = selected_pairs if selected_pairs else CURRENCY_PAIRS.copy()
        self.timeframe = timeframe
        self.timeframe_config = TIMEFRAME_CONFIG[timeframe]
        self.selected_strategies = selected_strategies if selected_strategies else ['S1', 'S2', 'S3', 'S4', 'S5', 'S6', 'S7', 'S8', 'S9', 'S10']
        
    def start(self):
        """Start the live trading bot"""
        print_header("🚀 LIVE TRADING BOT STARTED")
        print_colored(f"📊 Monitoring {len(self.pairs)} currency pairs", "INFO")
        print_colored(f"⏰ Timeframe: {self.timeframe_config['display_name']}", "INFO")
        print_colored(f"📈 Granularity: {self.timeframe_config['granularity']}", "INFO")
        print_colored(f"⏱️  Signal interval: Every {self.timeframe_config['interval_seconds']} seconds", "INFO")
        print_colored(f"🎯 Strategies: {', '.join(self.selected_strategies)}", "INFO")
        print_colored(f"🎯 Minimum confidence: {TRADING_CONFIG['MIN_CONFIDENCE']*100}%", "INFO")
        print()

        self.running = True

        # Start the main trading loop
        self.trading_loop()
    
    def stop(self):
        """Stop the trading bot"""
        self.running = False
        print_colored("\n🛑 Trading bot stopped", "WARNING")
    
    def trading_loop(self):
        """Main trading loop with timeframe support"""
        while self.running:
            try:
                # Get timeframe-specific time info
                time_info = get_timeframe_time_info(self.timeframe)

                # Wait until fetch_before_seconds before next interval
                fetch_before = self.timeframe_config['fetch_before_seconds']
                if time_info['seconds_to_next_interval'] > fetch_before:
                    sleep_time = time_info['seconds_to_next_interval'] - fetch_before
                    print_colored(f"⏳ Waiting {sleep_time} seconds until next {self.timeframe_config['display_name']} scan...", "INFO")
                    time.sleep(sleep_time)

                # Perform market scan
                self.scan_markets()

                # FIXED: Calculate time to next interval boundary properly
                # Get fresh time info after the scan
                time_info_after_scan = get_timeframe_time_info(self.timeframe)

                # Calculate remaining time to next interval boundary
                remaining_time = time_info_after_scan['seconds_to_next_interval']

                # If we're very close to the next interval (less than 5 seconds), wait for the one after
                if remaining_time < 5:
                    remaining_time += self.timeframe_config['interval_seconds']

                print_colored(f"⏳ Next scan in {remaining_time} seconds ({self.timeframe_config['display_name']})...", "INFO")
                time.sleep(remaining_time)

            except KeyboardInterrupt:
                print_colored("\n⚠️  Interrupted by user", "WARNING")
                break
            except Exception as e:
                print_colored(f"❌ Error in trading loop: {str(e)}", "ERROR")
                time.sleep(5)  # Wait before retrying
    
    def scan_markets(self):
        """Scan all currency pairs for trading signals"""
        current_time = datetime.now()
        print_header(f"📊 {self.timeframe_config['display_name'].upper()} MARKET SCAN - {current_time.strftime('%Y-%m-%d %H:%M:%S')}")

        # Print signal table header
        print_signal_table_header()

        signals_found = 0

        for pair in self.pairs:
            try:
                # Fetch live data with timeframe-specific granularity
                df = fetch_live_candles(pair, TRADING_CONFIG['LOOKBACK_CANDLES'], self.timeframe_config['granularity'])

                if df is not None and len(df) > 50:
                    # Evaluate strategies with selected strategies
                    signal_data = self.strategy_engine.evaluate_all_strategies(df, self.selected_strategies)

                    # Format output
                    formatted_output = format_signal_output(pair, signal_data)

                    # Display result in horizontal format
                    if signal_data['signal'] != 'HOLD':
                        signals_found += 1
                        print_signal_row(
                            date=formatted_output['date'],
                            time=formatted_output['time'],
                            pair=pair,
                            price=formatted_output['price'],
                            signal=signal_data['signal'],
                            confidence=formatted_output['confidence'],
                            strategy=signal_data['strategy'],
                            is_no_signal=False
                        )
                    else:
                        print_signal_row(
                            date=formatted_output['date'],
                            time=formatted_output['time'],
                            pair=pair,
                            price=formatted_output['price'],
                            is_no_signal=True
                        )
                else:
                    # Data fetch failed - show as no signal
                    time_info = get_current_time_info()
                    print_signal_row(
                        date=time_info['current_time'].strftime('%Y-%m-%d'),
                        time=time_info['current_time'].strftime('%H:%M:%S'),
                        pair=pair,
                        price="ERROR",
                        is_no_signal=True
                    )

            except Exception as e:
                print_colored(f"❌ Error scanning {pair}: {str(e)}", "ERROR")

        # Summary
        print_colored("=" * 120, "HEADER")
        if signals_found > 0:
            print_colored(f"✅ Found {signals_found} trading signals", "SUCCESS", bold=True)
        else:
            print_colored("ℹ️  No trading signals found", "INFO")
        print()
    
    def log_signal(self, formatted_output, signal_data):
        """Log detailed signal information"""
        print_colored(f"\n🎯 SIGNAL DETECTED:", "SUCCESS", bold=True)
        print_colored(f"   📅 Date: {formatted_output['date']}", "INFO")
        print_colored(f"   🕐 Time: {formatted_output['time']}", "INFO")
        print_colored(f"   💱 Pair: {formatted_output['pair']}", "INFO")
        print_colored(f"   📈 Direction: {signal_data['signal']}", formatted_output['color'], bold=True)
        print_colored(f"   💰 Price: {formatted_output['price']}", "INFO")
        print_colored(f"   🎯 Confidence: {formatted_output['confidence']}", "SUCCESS")
        print_colored(f"   🔧 Strategy: {signal_data['strategy']}", "INFO")
        
        # Show all strategy signals
        print_colored(f"   📊 All Strategies:", "INFO")
        for strategy, data in signal_data['all_signals'].items():
            signal_text = "BUY" if data['signal'] == 1 else "SELL" if data['signal'] == -1 else "HOLD"
            conf_text = f"{data['confidence']*100:.1f}%" if data['confidence'] > 0 else "-"
            print_colored(f"      {strategy}: {signal_text} ({conf_text})", "INFO")
        print()

def main(selected_pairs=None, timeframe="1min", selected_strategies=None):
    """Main function"""
    try:
        # Create and start the trading bot
        bot = LiveTradingBot(selected_pairs, timeframe, selected_strategies)

        print_colored("🤖 Live Trading Bot Initializing...", "INFO", bold=True)
        print_colored("Press Ctrl+C to stop the bot", "WARNING")
        print()

        # Start the bot
        bot.start()

    except KeyboardInterrupt:
        print_colored("\n👋 Goodbye! Trading bot stopped.", "INFO")
    except Exception as e:
        print_colored(f"❌ Fatal error: {str(e)}", "ERROR")

if __name__ == "__main__":
    main()
